server.port=8082

# MongoDB
spring.data.mongodb.uri=mongodb://localhost:27017/backend-db
spring.data.mongodb.database=backend-db

# JWT & OAuth2
spring.security.oauth2.resourceserver.jwt.issuer-uri=http://localhost:8080
spring.security.oauth2.client.registration.google.client-id=YOUR_CLIENT_ID
spring.security.oauth2.client.registration.google.client-secret=YOUR_CLIENT_SECRET
spring.security.oauth2.client.registration.google.scope=profile,email
spring.security.oauth2.client.registration.google.redirect-uri=http://localhost:8080/login
spring.security.oauth2.client.provider.google.authorization-uri=https://accounts.google.com/o/oauth2/auth
spring.security.oauth2.client.provider.google.token-uri=https://oauth2.googleapis.com/token
spring.security.oauth2.client.provider.google.user-info-uri=https://www.googleapis.com/oauth2/v3/userinfo

# File upload configuration
# Chemin relatif pour l'environnement de developpement
uploadFolder=./uploads
# Inclure a la fois le dossier d'uploads et les ressources statiques classiques
spring.web.resources.static-locations=file:${uploadFolder},classpath:/static/
spring.mvc.static-path-pattern=/content/**
# Activer l'acces aux ressources statiques
spring.resources.add-mappings=true

# Multipart
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-request-size=1GB
spring.servlet.multipart.max-file-size=1GB

# LLaMA API
llama.api.url=http://************:11434

# Gradio API endpoints
gradio.api.file.upload=http://127.0.0.1:8000/api/upload_file
gradio.api.queue.join=http://127.0.0.1:7860/gradio_api/queue/join
gradio.api.queue.data=http://127.0.0.1:7860/gradio_api/queue/data

# Optional: session hash (only if needed for consistent session identification)
gradio.session.hash=aaaaaaaa

gradio.poll.maxAttempts=30
gradio.poll.delayMs=2000




# Timeout
spring.mvc.async.request-timeout=10h
spring.main.allow-bean-definition-overriding=true

# Logging
logging.level.org.example=TRACE
logging.level.org.springframework.web=TRACE
logging.level.org.springframework.security=TRACE


spring.mail.host=smtp.mailersend.net
spring.mail.port=587
spring.mail.from=<EMAIL>
spring.mail.username=<EMAIL>
spring.mail.password=mssp.LJIc4Dj.jpzkmgq9k2m4059v.NLNA7SY
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
