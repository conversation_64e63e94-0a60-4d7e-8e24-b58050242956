@echo off
echo Démarrage de l'application Spring Boot...

REM Vérifier si le port 8081 est libre
netstat -ano | findstr :8081 > nul
if %errorlevel% == 0 (
    echo Le port 8081 est occupé. Tentative d'arrêt des processus...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8081 ^| findstr LISTENING') do (
        echo Arrêt du processus %%a
        taskkill /PID %%a /F > nul 2>&1
    )
    timeout /t 3 > nul
)

REM Démarrer l'application
echo Démarrage de l'application sur le port 8081...

REM Essayer avec Maven si disponible
where mvn > nul 2>&1
if %errorlevel% == 0 (
    echo Utilisation de Maven...
    mvn spring-boot:run
    goto :end
)

REM Essayer avec le wrapper Maven
if exist mvnw.cmd (
    echo Utilisation du wrapper Maven...
    mvnw.cmd spring-boot:run
    goto :end
)

REM Essayer avec Java directement
where java > nul 2>&1
if %errorlevel% == 0 (
    if exist target\*.jar (
        echo Utilisation de Java directement...
        java -jar target\*.jar
        goto :end
    )
)

echo Erreur: Impossible de démarrer l'application.
echo Vérifiez que Java et Maven sont installés et dans le PATH.
pause

:end
