Write-Host "Démarrage de l'application Spring Boot..." -ForegroundColor Green

# Vérifier si le port 8082 est libre
$portCheck = netstat -ano | Select-String ":8082.*LISTENING"
if ($portCheck) {
    Write-Host "Le port 8082 est occupé. Tentative d'arrêt des processus..." -ForegroundColor Yellow
    $processes = netstat -ano | Select-String ":8082.*LISTENING" | ForEach-Object {
        ($_ -split '\s+')[-1]
    }
    foreach ($pid in $processes) {
        try {
            Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
            Write-Host "Processus $pid arrêté" -ForegroundColor Yellow
        } catch {
            Write-Host "Impossible d'arrêter le processus $pid" -ForegroundColor Red
        }
    }
    Start-Sleep -Seconds 3
}

Write-Host "Démarrage de l'application sur le port 8082..." -ForegroundColor Green

# Essayer avec Maven si disponible
if (Get-Command mvn -ErrorAction SilentlyContinue) {
    Write-Host "Utilisation de Maven..." -ForegroundColor Cyan
    mvn spring-boot:run
    exit
}

# Essayer avec le wrapper Maven
if (Test-Path "mvnw.cmd") {
    Write-Host "Utilisation du wrapper Maven..." -ForegroundColor Cyan
    .\mvnw.cmd spring-boot:run
    exit
}

# Essayer avec Java directement
if (Get-Command java -ErrorAction SilentlyContinue) {
    $jarFile = Get-ChildItem -Path "target" -Filter "*.jar" | Select-Object -First 1
    if ($jarFile) {
        Write-Host "Utilisation de Java directement..." -ForegroundColor Cyan
        java -jar $jarFile.FullName
        exit
    }
}

Write-Host "Erreur: Impossible de démarrer l'application." -ForegroundColor Red
Write-Host "Vérifiez que Java et Maven sont installés et dans le PATH." -ForegroundColor Red
Write-Host ""
Write-Host "Solutions alternatives:" -ForegroundColor Yellow
Write-Host "1. Utilisez votre IDE (IntelliJ IDEA, Eclipse, VS Code) pour démarrer l'application" -ForegroundColor White
Write-Host "2. Installez Maven: https://maven.apache.org/download.cgi" -ForegroundColor White
Write-Host "3. Installez Java JDK: https://adoptium.net/" -ForegroundColor White
Read-Host "Appuyez sur Entrée pour continuer"
